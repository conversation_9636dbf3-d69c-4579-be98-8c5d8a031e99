@import './base.css';

#app {
  max-width: none;
  margin: 0;
  padding: 0;
  font-weight: normal;
  width: 100%;
  min-height: 100vh;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  /* 移除默认的居中和网格布局，保持管理系统的全宽布局 */
  body {
    display: block;
  }

  #app {
    display: block;
    width: 100%;
    padding: 0;
  }
}
