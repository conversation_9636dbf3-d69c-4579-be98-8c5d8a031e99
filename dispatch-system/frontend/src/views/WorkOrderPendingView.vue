<template>
  <div class="work-order-pending page-container">
    <div class="page-header">
      <h2>待办查询</h2>
      <div>
        <el-button type="primary" @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="工单标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入工单标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="searchForm.priority" placeholder="请选择优先级" clearable style="width: 120px">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 工单列表 -->
    <el-card>
      <el-table
        :data="workOrders"
        v-loading="loading"
        stripe
        @row-click="viewDetail"
        style="cursor: pointer"
      >
        <el-table-column prop="order_number" label="工单号" width="120" />
        <el-table-column prop="title" label="标题" min-width="200" />
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator_name" label="创建人" width="100" />
        <el-table-column prop="created_at" label="创建时间" width="160" />
        <el-table-column prop="scheduled_start" label="计划开始" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click.stop="viewDetail(row)">
              查看详情
            </el-button>
            <el-button type="success" size="small" @click.stop="startWork(row)">
              开始处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

const router = useRouter()

const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  title: '',
  priority: '',
  dateRange: null as [string, string] | null
})

const workOrders = ref([
  {
    id: 1,
    order_number: 'WO20240115001',
    title: '空调设备维修',
    description: '办公室空调制冷效果差，需要检修',
    priority: 'high',
    status: 'pending',
    creator_name: '张三',
    created_at: '2024-01-15 10:30:00',
    scheduled_start: '2024-01-16 09:00:00'
  },
  {
    id: 2,
    order_number: 'WO20240115002',
    title: '网络故障排查',
    description: '三楼网络连接不稳定',
    priority: 'medium',
    status: 'pending',
    creator_name: '李四',
    created_at: '2024-01-15 14:20:00',
    scheduled_start: '2024-01-16 14:00:00'
  }
])

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    low: '',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger'
  }
  return typeMap[priority] || ''
}

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return textMap[priority] || priority
}

const loadWorkOrders = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      status: 'pending', // 只查询待办工单
      title: searchForm.title,
      priority: searchForm.priority,
      start_date: searchForm.dateRange?.[0],
      end_date: searchForm.dateRange?.[1]
    }

    // 这里应该调用API获取待办工单数据
    // const response = await api.getPendingWorkOrders(params)
    // workOrders.value = response.data.items
    // total.value = response.data.total
    
    // 模拟数据
    total.value = 2
  } catch (error) {
    ElMessage.error('加载待办工单失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadWorkOrders()
}

const resetSearch = () => {
  searchForm.title = ''
  searchForm.priority = ''
  searchForm.dateRange = null
  currentPage.value = 1
  loadWorkOrders()
}

const refreshData = () => {
  loadWorkOrders()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadWorkOrders()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadWorkOrders()
}

const viewDetail = (row: any) => {
  router.push(`/work-orders/${row.id}`)
}

const startWork = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要开始处理工单"${row.title}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里应该调用API更新工单状态为处理中
    // await api.updateWorkOrderStatus(row.id, 'processing')
    
    ElMessage.success('工单已开始处理')
    loadWorkOrders()
  } catch (error) {
    // 用户取消操作
  }
}

onMounted(() => {
  loadWorkOrders()
})
</script>

<style scoped>
.work-order-pending {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.search-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.page-container {
  padding: 20px;
}
</style>
