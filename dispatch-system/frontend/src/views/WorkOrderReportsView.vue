<template>
  <div class="work-order-reports page-container">
    <div class="page-header">
      <h2>工单报表</h2>
      <div>
        <el-button type="primary" @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="success" @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 时间筛选 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="统计时间">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="统计维度">
          <el-select v-model="filterForm.dimension" style="width: 120px">
            <el-option label="按天" value="day" />
            <el-option label="按周" value="week" />
            <el-option label="按月" value="month" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadReportData">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.total }}</div>
            <div class="stats-label">总工单数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.completed }}</div>
            <div class="stats-label">已完成</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.pending }}</div>
            <div class="stats-label">待处理</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.completionRate }}%</div>
            <div class="stats-label">完成率</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card title="工单状态分布">
          <template #header>
            <span>工单状态分布</span>
          </template>
          <div ref="statusChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card title="工单优先级分布">
          <template #header>
            <span>工单优先级分布</span>
          </template>
          <div ref="priorityChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card title="工单趋势图">
          <template #header>
            <span>工单创建趋势</span>
          </template>
          <div ref="trendChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>详细统计数据</span>
      </template>
      <el-table :data="detailData" v-loading="loading" stripe>
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="total" label="总数" width="80" />
        <el-table-column prop="pending" label="待处理" width="80" />
        <el-table-column prop="processing" label="处理中" width="80" />
        <el-table-column prop="completed" label="已完成" width="80" />
        <el-table-column prop="cancelled" label="已取消" width="80" />
        <el-table-column prop="completion_rate" label="完成率" width="100">
          <template #default="{ row }">
            {{ row.completion_rate }}%
          </template>
        </el-table-column>
        <el-table-column prop="avg_duration" label="平均处理时长" width="120" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'

const loading = ref(false)

const filterForm = reactive({
  dateRange: [
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    new Date().toISOString().split('T')[0]
  ] as [string, string],
  dimension: 'day'
})

const stats = ref({
  total: 156,
  completed: 128,
  pending: 18,
  processing: 10,
  completionRate: 82
})

const detailData = ref([
  {
    date: '2024-01-15',
    total: 12,
    pending: 2,
    processing: 3,
    completed: 7,
    cancelled: 0,
    completion_rate: 58,
    avg_duration: '4.5小时'
  },
  {
    date: '2024-01-14',
    total: 8,
    pending: 1,
    processing: 1,
    completed: 6,
    cancelled: 0,
    completion_rate: 75,
    avg_duration: '3.2小时'
  }
])

// 图表引用
const statusChartRef = ref<HTMLDivElement>()
const priorityChartRef = ref<HTMLDivElement>()
const trendChartRef = ref<HTMLDivElement>()

const loadReportData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      start_date: filterForm.dateRange[0],
      end_date: filterForm.dateRange[1],
      dimension: filterForm.dimension
    }

    // 这里应该调用API获取报表数据
    // const response = await api.getWorkOrderReports(params)
    // stats.value = response.data.stats
    // detailData.value = response.data.details
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 重新渲染图表
    await nextTick()
    renderCharts()
    
  } catch (error) {
    ElMessage.error('加载报表数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadReportData()
}

const exportReport = () => {
  // 这里应该实现报表导出功能
  ElMessage.info('报表导出功能开发中...')
}

const renderCharts = () => {
  // 这里应该使用图表库（如 ECharts）渲染图表
  // 由于没有安装图表库，这里只是占位
  if (statusChartRef.value) {
    statusChartRef.value.innerHTML = '<div style="text-align: center; line-height: 300px; color: #999;">状态分布图表（需要集成图表库）</div>'
  }
  if (priorityChartRef.value) {
    priorityChartRef.value.innerHTML = '<div style="text-align: center; line-height: 300px; color: #999;">优先级分布图表（需要集成图表库）</div>'
  }
  if (trendChartRef.value) {
    trendChartRef.value.innerHTML = '<div style="text-align: center; line-height: 400px; color: #999;">趋势图表（需要集成图表库）</div>'
  }
}

onMounted(() => {
  loadReportData()
})
</script>

<style scoped>
.work-order-reports {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-item {
  padding: 20px 0;
}

.stats-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.page-container {
  padding: 20px;
}
</style>
